import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { trigger, state, style, transition, animate, keyframes } from '@angular/animations';

@Component({
  selector: 'app-hero',
  imports: [],
  templateUrl: './hero.component.html',
  styleUrl: './hero.component.css',
  animations: [
    trigger('fadeInUp', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(30px)' }),
        animate('0.8s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ]),
    trigger('fadeInRight', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(50px)' }),
        animate('1s ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
      ])
    ]),
    trigger('bounce', [
      transition(':enter', [
        animate('2s ease-in-out', keyframes([
          style({ transform: 'translateY(0)', offset: 0 }),
          style({ transform: 'translateY(-10px)', offset: 0.5 }),
          style({ transform: 'translateY(0)', offset: 1 })
        ]))
      ])
    ])
  ]
})
export class HeroComponent implements OnInit, OnDestroy {
  currentTitle: string = '';
  private titles: string[] = [
    'AI-Powered Full Stack Developer',
    'Neural Network Engineer',
    'Machine Learning Enthusiast',
    'Cognitive Computing Specialist',
    'Digital Brain Architect',
    'Synaptic Code Creator',
    'Intelligent Systems Builder'
  ];
  private currentIndex: number = 0;
  private typingInterval: any;
  private titleInterval: any;

  ngOnInit() {
    this.startTypingAnimation();
  }

  ngOnDestroy() {
    if (this.typingInterval) {
      clearInterval(this.typingInterval);
    }
    if (this.titleInterval) {
      clearInterval(this.titleInterval);
    }
  }

  private startTypingAnimation() {
    this.typeTitle();

    this.titleInterval = setInterval(() => {
      this.currentIndex = (this.currentIndex + 1) % this.titles.length;
      this.currentTitle = '';
      this.typeTitle();
    }, 4000);
  }

  private typeTitle() {
    const title = this.titles[this.currentIndex];
    let charIndex = 0;

    this.typingInterval = setInterval(() => {
      if (charIndex < title.length) {
        this.currentTitle += title.charAt(charIndex);
        charIndex++;
      } else {
        clearInterval(this.typingInterval);
      }
    }, 100);
  }
}

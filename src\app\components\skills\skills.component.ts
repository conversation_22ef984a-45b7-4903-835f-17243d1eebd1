import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Skill {
  name: string;
  level: number;
}

interface Technology {
  name: string;
  icon: string;
  color: string;
}

@Component({
  selector: 'app-skills',
  imports: [CommonModule],
  templateUrl: './skills.component.html',
  styleUrl: './skills.component.css'
})
export class SkillsComponent {
  programmingSkills: Skill[] = [
    { name: 'Java', level: 85 },
    { name: 'JavaScript/TypeScript', level: 80 },
    { name: 'HTML/CSS', level: 90 },
    { name: 'SQL', level: 75 }
  ];

  frontendSkills: Skill[] = [
    { name: 'Angular', level: 85 },
    { name: 'React', level: 70 },
    { name: 'Bootstrap', level: 80 },
    { name: 'Responsive Design', level: 85 }
  ];

  backendSkills: Skill[] = [
    { name: 'Spring Boot', level: 80 },
    { name: 'Node.js', level: 70 },
    { name: 'REST APIs', level: 85 },
    { name: 'Database Design', level: 75 }
  ];

  toolsSkills: Skill[] = [
    { name: 'Git/GitHub', level: 85 },
    { name: 'IntelliJ IDEA', level: 90 },
    { name: 'VS Code', level: 85 },
    { name: 'Android Studio', level: 70 },
    { name: 'Figma', level: 65 },
    { name: 'Postman', level: 80 }
  ];

  technologies: Technology[] = [
    { name: 'Angular', icon: 'fab fa-angular', color: '#dd0031' },
    { name: 'Java', icon: 'fab fa-java', color: '#f89820' },
    { name: 'JavaScript', icon: 'fab fa-js-square', color: '#f7df1e' },
    { name: 'HTML5', icon: 'fab fa-html5', color: '#e34f26' },
    { name: 'CSS3', icon: 'fab fa-css3-alt', color: '#1572b6' },
    { name: 'GitHub', icon: 'fab fa-github', color: '#333' },
    { name: 'Bootstrap', icon: 'fab fa-bootstrap', color: '#7952b3' },
    { name: 'Node.js', icon: 'fab fa-node-js', color: '#339933' }
  ];
}

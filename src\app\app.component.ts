import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, RouterLinkActive, Router } from '@angular/router';
import { HeroComponent } from './components/hero/hero.component';
import { AboutComponent } from './components/about/about.component';
import { SkillsComponent } from './components/skills/skills.component';
import { EducationComponent } from './components/education/education.component';
import { ProjectsComponent } from './components/projects/projects.component';
import { ExperienceComponent } from './components/experience/experience.component';
import { ContactComponent } from './components/contact/contact.component';

interface NeuralNode {
  x: number;
  y: number;
  delay: number;
  active: boolean;
  id: number;
}

interface NeuralConnection {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  delay: number;
}

interface DataPulse {
  x: number;
  y: number;
  delay: number;
}

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    RouterOutlet,
    RouterLink,
    RouterLinkActive,
    HeroComponent,
    AboutComponent,
    SkillsComponent,
    EducationComponent,
    ProjectsComponent,
    ExperienceComponent,
    ContactComponent
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  title = 'Sachin Prabashwara - Full Stack Developer Portfolio';
  isScrolled = false;
  isMobileMenuOpen = false;
  isLoading = true;
  isDarkMode = false;
  neuralNodes: NeuralNode[] = [];
  neuralConnections: NeuralConnection[] = [];
  dataPulses: DataPulse[] = [];

  constructor(private router: Router) {
    // Initialize theme from localStorage
    this.isDarkMode = localStorage.getItem('theme') === 'dark';
    this.applyTheme();
  }

  ngOnInit() {
    // Add smooth scrolling behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Generate neural network
    this.generateNeuralNetwork();

    // Simulate loading time
    setTimeout(() => {
      this.isLoading = false;
    }, 3000);
  }

  generateNeuralNetwork() {
    // Generate neural nodes
    for (let i = 0; i < 30; i++) {
      this.neuralNodes.push({
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        delay: Math.random() * 5,
        active: Math.random() > 0.7,
        id: i
      });
    }

    // Generate connections between nearby nodes
    for (let i = 0; i < this.neuralNodes.length; i++) {
      for (let j = i + 1; j < this.neuralNodes.length; j++) {
        const node1 = this.neuralNodes[i];
        const node2 = this.neuralNodes[j];
        const distance = Math.sqrt(
          Math.pow(node1.x - node2.x, 2) + Math.pow(node1.y - node2.y, 2)
        );

        // Connect nodes that are close enough
        if (distance < 200 && Math.random() > 0.6) {
          this.neuralConnections.push({
            x1: node1.x,
            y1: node1.y,
            x2: node2.x,
            y2: node2.y,
            delay: Math.random() * 3
          });
        }
      }
    }

    // Generate data pulses
    for (let i = 0; i < 15; i++) {
      this.dataPulses.push({
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        delay: Math.random() * 4
      });
    }
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    this.isScrolled = window.pageYOffset > 50;
  }

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80; // Account for fixed navbar
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
    this.isMobileMenuOpen = false;
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
    localStorage.setItem('theme', this.isDarkMode ? 'dark' : 'light');
    this.applyTheme();
  }

  private applyTheme() {
    if (this.isDarkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }

  // Navigation helper for single page sections
  navigateToSection(section: string) {
    this.router.navigate(['/home']).then(() => {
      setTimeout(() => {
        this.scrollToSection(section);
      }, 100);
    });
  }
}

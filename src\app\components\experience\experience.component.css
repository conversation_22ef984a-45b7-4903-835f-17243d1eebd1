/* Blue Experience Container */
.experience-container {
  padding: 5rem 0;
  background: linear-gradient(135deg, #1e293b 0%, #0c1844 100%);
  position: relative;
  overflow: hidden;
}

.experience-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="exp-pattern" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="2" fill="%23667eea" opacity="0.1"/><circle cx="15" cy="15" r="1" fill="%23764ba2" opacity="0.1"/><circle cx="45" cy="45" r="1" fill="%23667eea" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23exp-pattern)"/></svg>');
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #666;
  position: relative;
}

.section-subtitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* Experience Content */
.experience-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Experience Timeline */
.experience-timeline {
  position: relative;
  padding: 2rem 0;
}

.timeline-line {
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.experience-item {
  position: relative;
  margin-bottom: 3rem;
  padding-left: 80px;
  opacity: 0;
  transform: translateX(-50px);
  animation: slideInLeft 0.8s ease-out forwards;
}

.experience-item:nth-child(2) {
  animation-delay: 0.2s;
}

.experience-item:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Experience Marker */
.experience-marker {
  position: absolute;
  left: -50px;
  top: 20px;
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  z-index: 3;
}

.marker-inner {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.experience-item.current .marker-inner {
  background: linear-gradient(45deg, #28a745 0%, #20c997 100%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
}

/* Experience Card */
.experience-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.experience-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.experience-item.current .experience-card::before {
  background: linear-gradient(45deg, #28a745 0%, #20c997 100%);
}

.experience-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 2rem 2rem 1rem;
}

.job-info h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.job-info h4 {
  font-size: 1.1rem;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 1rem;
}

.job-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.duration,
.location {
  font-size: 0.9rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.duration::before {
  content: '📅';
}

.location::before {
  content: '📍';
}

.status {
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.current-badge {
  background: #d4edda;
  color: #155724;
}

.training-badge {
  background: #d1ecf1;
  color: #0c5460;
}

.card-body {
  padding: 0 2rem 2rem;
}

.job-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.responsibilities,
.technologies,
.achievements {
  margin-bottom: 1.5rem;
}

.responsibilities h5,
.technologies h5,
.achievements h5 {
  font-size: 1rem;
  color: #333;
  margin-bottom: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.responsibilities h5::before {
  content: '📋';
}

.technologies h5::before {
  content: '🛠️';
}

.achievements h5::before {
  content: '🏆';
}

.responsibilities ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.responsibilities li {
  padding: 0.5rem 0;
  padding-left: 1.5rem;
  position: relative;
  font-size: 0.95rem;
  color: #666;
  line-height: 1.5;
}

.responsibilities li::before {
  content: '▸';
  position: absolute;
  left: 0;
  color: #667eea;
  font-weight: bold;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  background: #f8f9fa;
  color: #667eea;
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.tech-tag:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.achievement-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 0.95rem;
  color: #666;
}

.achievement-item i {
  color: #ffd700;
  font-size: 1rem;
}

/* Experience Stats */
.experience-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.stat-info h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.2rem;
}

.stat-info p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .experience-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .experience-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .experience-item {
    padding-left: 60px;
  }

  .experience-marker {
    left: -40px;
    width: 50px;
    height: 50px;
  }

  .marker-inner {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .timeline-line {
    left: 25px;
  }

  .card-header,
  .card-body {
    padding: 1.5rem;
  }

  .job-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .experience-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .experience-item {
    padding-left: 40px;
  }

  .experience-marker {
    left: -30px;
    width: 40px;
    height: 40px;
  }

  .marker-inner {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }

  .timeline-line {
    left: 20px;
  }

  .experience-card {
    margin-left: -10px;
  }
}
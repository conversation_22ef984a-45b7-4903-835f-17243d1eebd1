/* UNIFIED BLUE COLOR THEME */
:root {
  /* Core Blue Palette */
  --blue-950: #0a0f2e;
  --blue-900: #0c1844;
  --blue-800: #1e3a8a;
  --blue-700: #1d4ed8;
  --blue-600: #2563eb;
  --blue-500: #3b82f6;
  --blue-400: #60a5fa;
  --blue-300: #93c5fd;
  --blue-200: #bfdbfe;
  --blue-100: #dbeafe;

  /* Background Colors */
  --bg-primary: var(--blue-950);
  --bg-secondary: var(--blue-900);
  --bg-surface: var(--blue-800);
  --bg-card: rgba(30, 58, 138, 0.9);
  --bg-hover: rgba(37, 99, 235, 0.2);

  /* Text Colors */
  --text-white: #ffffff;
  --text-blue-100: var(--blue-100);
  --text-blue-200: var(--blue-200);
  --text-blue-300: var(--blue-300);

  /* Blue Gradients */
  --gradient-primary: linear-gradient(135deg, var(--blue-950) 0%, var(--blue-800) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--blue-800) 0%, var(--blue-600) 100%);
  --gradient-accent: linear-gradient(135deg, var(--blue-600) 0%, var(--blue-400) 100%);

  /* Effects */
  --shadow-blue: 0 4px 20px rgba(59, 130, 246, 0.3);
  --shadow-glow: 0 0 30px rgba(96, 165, 250, 0.5);
  --border-blue: 1px solid rgba(96, 165, 250, 0.3);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-white);
  background: var(--bg-primary);
  overflow-x: hidden;
}

/* Blue Particles Background */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  background: radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(29, 78, 216, 0.1) 0%, transparent 50%);
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: var(--blue-400);
  box-shadow: 0 0 10px var(--blue-400);
  opacity: 0.8;
  animation: blueParticleFloat 10s ease-in-out infinite;
}

@keyframes blueParticleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.8;
    box-shadow: 0 0 10px var(--blue-400);
  }
  25% {
    transform: translateY(-120px) translateX(60px) scale(1.3);
    opacity: 1;
    box-shadow: 0 0 20px var(--blue-300);
  }
  50% {
    transform: translateY(-60px) translateX(-40px) scale(0.7);
    opacity: 0.6;
    box-shadow: 0 0 15px var(--blue-500);
  }
  75% {
    transform: translateY(-180px) translateX(30px) scale(1.1);
    opacity: 0.9;
    box-shadow: 0 0 25px var(--blue-400);
  }
}

/* Blue Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: all 0.5s ease;
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
  transform: scale(1.1);
}

.loading-content {
  text-align: center;
  color: var(--text-white);
  animation: fadeInUp 1s ease-out;
}

.loading-logo {
  width: 120px;
  height: 120px;
  background: rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  backdrop-filter: blur(10px);
  border: 3px solid var(--blue-400);
  box-shadow: var(--shadow-glow);
  animation: bluePulse 2s infinite;
}

.loading-logo span {
  font-size: 2.8rem;
  font-weight: 700;
  color: var(--text-white);
  text-shadow: 0 0 20px var(--blue-400);
}

.loading-text h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  animation: slideInLeft 1s ease-out 0.3s both;
}

.loading-text p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  animation: slideInRight 1s ease-out 0.6s both;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  animation: fadeIn 1s ease-out 0.9s both;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bluePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--shadow-glow);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 40px rgba(96, 165, 250, 0.7);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Blue Navigation Styles */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(10, 15, 46, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-blue);
  border-bottom: var(--border-blue);
}

.navbar.scrolled {
  padding: 0.5rem 0;
  background: rgba(10, 15, 46, 0.98);
  box-shadow: 0 2px 30px rgba(59, 130, 246, 0.4);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Enhanced Professional Logo */
.nav-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  padding: 0.5rem;
  border-radius: 12px;
}

.nav-logo:hover {
  transform: translateY(-2px);
  background: rgba(96, 165, 250, 0.1);
}

.logo-icon {
  width: 45px;
  height: 45px;
  background: var(--gradient-accent);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: logoRotate 8s linear infinite;
}

.logo-initial {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  z-index: 2;
  position: relative;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.logo-name {
  font-size: 1.2rem;
  font-weight: 700;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 8px rgba(96, 165, 250, 0.3));
  line-height: 1;
}

.logo-title {
  font-size: 0.75rem;
  color: var(--text-blue-300);
  font-weight: 500;
  opacity: 0.9;
  line-height: 1;
}

@keyframes logoRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Navigation Menu */
.nav-menu {
  display: flex;
  list-style: none;
  gap: 0.5rem;
  align-items: center;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: var(--text-blue-200);
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  font-size: 0.95rem;
  overflow: hidden;
}

.nav-link i {
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.1), transparent);
  transition: left 0.6s ease;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-white);
  background: var(--bg-hover);
  text-shadow: 0 0 10px var(--blue-400);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.nav-link:hover::before,
.nav-link.active::before {
  left: 100%;
}

.nav-link:hover i,
.nav-link.active i {
  color: var(--blue-300);
  transform: scale(1.1);
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gradient-accent);
  transition: width 0.4s ease;
  box-shadow: 0 0 10px var(--blue-400);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
}

/* Navigation Actions */
.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-left: 1rem;
}

.theme-toggle {
  width: 40px;
  height: 40px;
  background: rgba(96, 165, 250, 0.1);
  border: 1px solid rgba(96, 165, 250, 0.2);
  border-radius: 10px;
  color: var(--text-blue-200);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.theme-toggle:hover {
  background: rgba(96, 165, 250, 0.2);
  color: var(--text-white);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.hamburger:hover {
  background: rgba(102, 126, 234, 0.1);
}

.bar {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

.hamburger.active .bar:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active .bar:nth-child(2) {
  opacity: 0;
}

.hamburger.active .bar:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

/* Enhanced Main Content */
.main-content {
  margin-top: 80px;
  position: relative;
  overflow-x: hidden;
}

.main-content section {
  min-height: 100vh;
  padding: 0;
  position: relative;
  scroll-margin-top: 80px;
}

/* Section-specific styling */
.section-hero {
  background: var(--gradient-primary);
  position: relative;
  overflow: hidden;
}

.section-about {
  background: var(--bg-secondary);
  position: relative;
}

.section-skills {
  background: var(--bg-primary);
  position: relative;
}

.section-education {
  background: var(--bg-secondary);
  position: relative;
}

.section-projects {
  background: var(--bg-primary);
  position: relative;
}

.section-experience {
  background: var(--bg-secondary);
  position: relative;
}

.section-contact {
  background: var(--bg-primary);
  position: relative;
}

/* Section transitions */
.main-content section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(96, 165, 250, 0.3) 50%,
    transparent 100%);
  z-index: 10;
}

.main-content section:first-child::before {
  display: none;
}

/* Enhanced Professional Footer */
.footer {
  background: var(--gradient-secondary);
  color: var(--text-white);
  padding: 3rem 0 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 20%, rgba(96, 165, 250, 0.1) 0%, transparent 60%),
    radial-gradient(circle at 70% 80%, rgba(29, 78, 216, 0.08) 0%, transparent 60%);
  animation: footerPulse 15s ease-in-out infinite;
}

@keyframes footerPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.9; }
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.footer-content p {
  font-size: 1rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  color: var(--text-white);
  font-size: 1.3rem;
  text-decoration: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px) scale(1.05);
  box-shadow:
    0 15px 40px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.social-link:hover::before {
  left: 100%;
}

/* Footer bottom */
.footer::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(96, 165, 250, 0.5) 50%,
    transparent 100%);
}

/* Scroll to Top Button */
.scroll-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  z-index: 1000;
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.scroll-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.scroll-to-top:active {
  transform: translateY(-1px);
}

/* Dark Theme Support */
.dark-theme {
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-surface: #2a2a2a;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-hover: rgba(96, 165, 250, 0.15);

  --text-white: #ffffff;
  --text-blue-100: #e0f2fe;
  --text-blue-200: #b3e5fc;
  --text-blue-300: #81d4fa;
}

.dark-theme .navbar {
  background: rgba(10, 10, 10, 0.95);
  border-bottom: 1px solid rgba(96, 165, 250, 0.2);
}

.dark-theme .nav-link:hover {
  background: rgba(96, 165, 250, 0.15);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .nav-container {
    padding: 0 1.5rem;
  }

  .logo-text {
    display: none;
  }

  .nav-actions {
    margin-left: 0.5rem;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0.75rem 0;
  }

  .nav-container {
    padding: 0 1rem;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background: rgba(10, 15, 46, 0.98);
    backdrop-filter: blur(20px);
    width: 100%;
    height: calc(100vh - 70px);
    padding: 2rem 0;
    transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 50px rgba(59, 130, 246, 0.2);
    border-right: 1px solid rgba(96, 165, 250, 0.2);
    gap: 0;
    justify-content: flex-start;
    overflow-y: auto;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-item {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .nav-link {
    width: calc(100% - 2rem);
    margin: 0 1rem;
    justify-content: flex-start;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
  }

  .nav-link i {
    font-size: 1.2rem;
    margin-right: 0.5rem;
  }

  .hamburger {
    display: flex;
  }

  .nav-actions {
    order: -1;
    margin-left: 0;
    margin-right: 1rem;
  }

  .theme-toggle {
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 0.5rem 0;
  }

  .nav-container {
    padding: 0 0.75rem;
  }

  .logo-icon {
    width: 35px;
    height: 35px;
  }

  .logo-initial {
    font-size: 1.2rem;
  }

  .theme-toggle {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }
}
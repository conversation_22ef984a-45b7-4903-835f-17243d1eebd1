/* UNIFIED BLUE COLOR THEME */
:root {
  /* Core Blue Palette */
  --blue-950: #0a0f2e;
  --blue-900: #0c1844;
  --blue-800: #1e3a8a;
  --blue-700: #1d4ed8;
  --blue-600: #2563eb;
  --blue-500: #3b82f6;
  --blue-400: #60a5fa;
  --blue-300: #93c5fd;
  --blue-200: #bfdbfe;
  --blue-100: #dbeafe;

  /* Background Colors */
  --bg-primary: var(--blue-950);
  --bg-secondary: var(--blue-900);
  --bg-surface: var(--blue-800);
  --bg-card: rgba(30, 58, 138, 0.9);
  --bg-hover: rgba(37, 99, 235, 0.2);

  /* Text Colors */
  --text-white: #ffffff;
  --text-blue-100: var(--blue-100);
  --text-blue-200: var(--blue-200);
  --text-blue-300: var(--blue-300);

  /* Blue Gradients */
  --gradient-primary: linear-gradient(135deg, var(--blue-950) 0%, var(--blue-800) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--blue-800) 0%, var(--blue-600) 100%);
  --gradient-accent: linear-gradient(135deg, var(--blue-600) 0%, var(--blue-400) 100%);

  /* Effects */
  --shadow-blue: 0 4px 20px rgba(59, 130, 246, 0.3);
  --shadow-glow: 0 0 30px rgba(96, 165, 250, 0.5);
  --border-blue: 1px solid rgba(96, 165, 250, 0.3);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-white);
  background: var(--bg-primary);
  overflow-x: hidden;
}

/* Blue Particles Background */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  background: radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(29, 78, 216, 0.1) 0%, transparent 50%);
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: var(--blue-400);
  box-shadow: 0 0 10px var(--blue-400);
  opacity: 0.8;
  animation: blueParticleFloat 10s ease-in-out infinite;
}

@keyframes blueParticleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.8;
    box-shadow: 0 0 10px var(--blue-400);
  }
  25% {
    transform: translateY(-120px) translateX(60px) scale(1.3);
    opacity: 1;
    box-shadow: 0 0 20px var(--blue-300);
  }
  50% {
    transform: translateY(-60px) translateX(-40px) scale(0.7);
    opacity: 0.6;
    box-shadow: 0 0 15px var(--blue-500);
  }
  75% {
    transform: translateY(-180px) translateX(30px) scale(1.1);
    opacity: 0.9;
    box-shadow: 0 0 25px var(--blue-400);
  }
}

/* Blue Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: all 0.5s ease;
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
  transform: scale(1.1);
}

.loading-content {
  text-align: center;
  color: var(--text-white);
  animation: fadeInUp 1s ease-out;
}

.loading-logo {
  width: 120px;
  height: 120px;
  background: rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  backdrop-filter: blur(10px);
  border: 3px solid var(--blue-400);
  box-shadow: var(--shadow-glow);
  animation: bluePulse 2s infinite;
}

.loading-logo span {
  font-size: 2.8rem;
  font-weight: 700;
  color: var(--text-white);
  text-shadow: 0 0 20px var(--blue-400);
}

.loading-text h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  animation: slideInLeft 1s ease-out 0.3s both;
}

.loading-text p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  animation: slideInRight 1s ease-out 0.6s both;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  animation: fadeIn 1s ease-out 0.9s both;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bluePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--shadow-glow);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 40px rgba(96, 165, 250, 0.7);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Blue Navigation Styles */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(10, 15, 46, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-blue);
  border-bottom: var(--border-blue);
}

.navbar.scrolled {
  padding: 0.5rem 0;
  background: rgba(10, 15, 46, 0.98);
  box-shadow: 0 2px 30px rgba(59, 130, 246, 0.4);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.nav-logo:hover {
  transform: scale(1.05);
}

.nav-logo span {
  font-size: 1.5rem;
  font-weight: bold;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 10px rgba(96, 165, 250, 0.5));
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: var(--text-blue-200);
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

.nav-link:hover {
  color: var(--text-white);
  background: var(--bg-hover);
  text-shadow: 0 0 10px var(--blue-400);
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gradient-accent);
  transition: width 0.3s ease;
  box-shadow: 0 0 10px var(--blue-400);
}

.nav-link:hover::after {
  width: 80%;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.hamburger:hover {
  background: rgba(102, 126, 234, 0.1);
}

.bar {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

.hamburger.active .bar:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active .bar:nth-child(2) {
  opacity: 0;
}

.hamburger.active .bar:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

/* Main Content */
main {
  margin-top: 80px;
}

section {
  min-height: 100vh;
  padding: 2rem 0;
}

/* Footer Styles */
.footer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  text-align: center;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.social-links {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.social-link {
  color: white;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

/* Scroll to Top Button */
.scroll-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  z-index: 1000;
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.scroll-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.scroll-to-top:active {
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .hamburger {
    display: flex;
  }

  .nav-container {
    padding: 0 1rem;
  }
}
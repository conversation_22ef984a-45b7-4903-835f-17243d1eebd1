/* Blue Hero Container */
.hero-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0f2e 0%, #1e3a8a 100%);
}

/* Animated Background */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(96, 165, 250, 0.3);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  animation: blueFloat 8s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 30%;
  animation-delay: 1s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  top: 10%;
  right: 5%;
  animation-delay: 3s;
}

@keyframes blueFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  25% {
    transform: translateY(-30px) rotate(90deg) scale(1.1);
    box-shadow: 0 0 30px rgba(96, 165, 250, 0.6);
  }
  50% {
    transform: translateY(-15px) rotate(180deg) scale(0.9);
    box-shadow: 0 0 25px rgba(29, 78, 216, 0.5);
  }
  75% {
    transform: translateY(-40px) rotate(270deg) scale(1.05);
    box-shadow: 0 0 35px rgba(59, 130, 246, 0.7);
  }
}

/* Hero Content */
.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  width: 100%;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

/* Blue Hero Text */
.hero-text {
  color: #ffffff;
}

.greeting {
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #93c5fd;
  text-shadow: 0 0 10px rgba(147, 197, 253, 0.5);
}

.wave {
  font-size: 1.8rem;
  animation: blueWave 2.5s ease-in-out infinite;
  filter: drop-shadow(0 0 5px rgba(147, 197, 253, 0.6));
}

@keyframes blueWave {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    filter: drop-shadow(0 0 5px rgba(147, 197, 253, 0.6));
  }
  25% {
    transform: rotate(25deg) scale(1.1);
    filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.8));
  }
  75% {
    transform: rotate(-15deg) scale(1.05);
    filter: drop-shadow(0 0 8px rgba(147, 197, 253, 0.7));
  }
}

.hero-name {
  font-size: 4.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.first-name {
  display: block;
  background: linear-gradient(45deg, #ffffff 0%, #bfdbfe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: blueNameGlow 3s ease-in-out infinite;
}

.last-name {
  display: block;
  background: linear-gradient(45deg, #60a5fa 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: blueNameGlow 3s ease-in-out infinite 0.5s;
}

@keyframes blueNameGlow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(96, 165, 250, 0.6));
  }
}

.hero-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  min-height: 2rem;
}

.typing-text {
  color: #ffd700;
  font-weight: 600;
}

.cursor {
  animation: blink 1s infinite;
  color: #ffd700;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.hero-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Hero Buttons */
.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}
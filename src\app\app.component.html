<!-- Professional Navigation Header -->
<nav class="navbar" [class.scrolled]="isScrolled">
  <div class="nav-container">
    <div class="nav-logo" routerLink="/home">
      <div class="logo-icon">
        <span class="logo-initial">S</span>
      </div>
      <div class="logo-text">
        <span class="logo-name">Sachin Prabashwara</span>
        <span class="logo-title">Full Stack Developer</span>
      </div>
    </div>

    <ul class="nav-menu" [class.active]="isMobileMenuOpen">
      <li class="nav-item">
        <a routerLink="/home" class="nav-link" routerLinkActive="active" (click)="closeMobileMenu()">
          <i class="fas fa-home"></i>
          <span>Home</span>
        </a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('about')" class="nav-link">
          <i class="fas fa-user"></i>
          <span>About</span>
        </a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('skills')" class="nav-link">
          <i class="fas fa-code"></i>
          <span>Skills</span>
        </a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('education')" class="nav-link">
          <i class="fas fa-graduation-cap"></i>
          <span>Education</span>
        </a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('projects')" class="nav-link">
          <i class="fas fa-folder-open"></i>
          <span>Projects</span>
        </a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('experience')" class="nav-link">
          <i class="fas fa-briefcase"></i>
          <span>Experience</span>
        </a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('contact')" class="nav-link">
          <i class="fas fa-envelope"></i>
          <span>Contact</span>
        </a>
      </li>
    </ul>

    <!-- Theme Toggle -->
    <div class="nav-actions">
      <button class="theme-toggle" (click)="toggleTheme()" title="Toggle Theme">
        <i class="fas fa-moon" *ngIf="!isDarkMode"></i>
        <i class="fas fa-sun" *ngIf="isDarkMode"></i>
      </button>
    </div>

    <div class="hamburger" [class.active]="isMobileMenuOpen" (click)="toggleMobileMenu()">
      <span class="bar"></span>
      <span class="bar"></span>
      <span class="bar"></span>
    </div>
  </div>
</nav>

<!-- Main Content - Single Page Layout -->
<main class="main-content">
  <!-- Hero Section -->
  <section id="hero" class="section-hero">
    <app-hero></app-hero>
  </section>

  <!-- About Section -->
  <section id="about" class="section-about">
    <app-about></app-about>
  </section>

  <!-- Skills Section -->
  <section id="skills" class="section-skills">
    <app-skills></app-skills>
  </section>

  <!-- Education Section -->
  <section id="education" class="section-education">
    <app-education></app-education>
  </section>

  <!-- Projects Section -->
  <section id="projects" class="section-projects">
    <app-projects></app-projects>
  </section>

  <!-- Experience Section -->
  <section id="experience" class="section-experience">
    <app-experience></app-experience>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="section-contact">
    <app-contact></app-contact>
  </section>
</main>

<!-- Loading Screen -->
<div class="loading-screen" [class.hidden]="!isLoading" *ngIf="isLoading">
  <div class="loading-content">
    <div class="loading-logo">
      <span>SP</span>
    </div>
    <div class="loading-text">
      <h2>Sachin Prabashwara</h2>
      <p>Full Stack Developer</p>
    </div>
    <div class="loading-spinner">
      <div class="spinner"></div>
    </div>
  </div>
</div>

<!-- Scroll to Top Button -->
<button
  class="scroll-to-top"
  [class.visible]="isScrolled"
  (click)="scrollToSection('hero')"
  title="Back to Top">
  <i class="fas fa-chevron-up"></i>
</button>

<!-- Footer -->
<footer class="footer">
  <div class="footer-content">
    <p>&copy; 2024 Sachin Prabashwara. All rights reserved.</p>
    <div class="social-links">
      <a href="https://www.linkedin.com/in/sachin-samarawickrama-349649312/" target="_blank" class="social-link">
        <i class="fab fa-linkedin"></i>
      </a>
      <a href="https://github.com/macamisp" target="_blank" class="social-link">
        <i class="fab fa-github"></i>
      </a>
      <a href="https://www.facebook.com/profile.php?id=100069446774540" target="_blank" class="social-link">
        <i class="fab fa-facebook"></i>
      </a>
      <a href="https://www.instagram.com/macami_impres/" target="_blank" class="social-link">
        <i class="fab fa-instagram"></i>
      </a>
      <a href="https://wa.me/94788179855" target="_blank" class="social-link">
        <i class="fab fa-whatsapp"></i>
      </a>
    </div>
  </div>
</footer>
<div class="skills-container">
  <div class="container">
    <div class="section-header animate-on-scroll">
      <h2 class="section-title">Skills & Technologies</h2>
      <div class="section-subtitle">My technical expertise and tools</div>
    </div>

    <div class="skills-content">
      <!-- Programming Languages -->
      <div class="skills-category animate-on-scroll">
        <h3 class="category-title">
          <i class="fas fa-code"></i>
          Programming Languages
        </h3>
        <div class="skills-grid">
          <div class="skill-item" *ngFor="let skill of programmingSkills">
            <div class="skill-info">
              <div class="skill-name">{{ skill.name }}</div>
              <div class="skill-percentage">{{ skill.level }}%</div>
            </div>
            <div class="skill-bar">
              <div class="skill-progress" [style.width.%]="skill.level" [attr.data-level]="skill.level"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Frontend Technologies -->
      <div class="skills-category animate-on-scroll">
        <h3 class="category-title">
          <i class="fas fa-paint-brush"></i>
          Frontend Technologies
        </h3>
        <div class="skills-grid">
          <div class="skill-item" *ngFor="let skill of frontendSkills">
            <div class="skill-info">
              <div class="skill-name">{{ skill.name }}</div>
              <div class="skill-percentage">{{ skill.level }}%</div>
            </div>
            <div class="skill-bar">
              <div class="skill-progress" [style.width.%]="skill.level" [attr.data-level]="skill.level"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Backend & Database -->
      <div class="skills-category animate-on-scroll">
        <h3 class="category-title">
          <i class="fas fa-server"></i>
          Backend & Database
        </h3>
        <div class="skills-grid">
          <div class="skill-item" *ngFor="let skill of backendSkills">
            <div class="skill-info">
              <div class="skill-name">{{ skill.name }}</div>
              <div class="skill-percentage">{{ skill.level }}%</div>
            </div>
            <div class="skill-bar">
              <div class="skill-progress" [style.width.%]="skill.level" [attr.data-level]="skill.level"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tools & Others -->
      <div class="skills-category animate-on-scroll">
        <h3 class="category-title">
          <i class="fas fa-tools"></i>
          Tools & Others
        </h3>
        <div class="skills-grid">
          <div class="skill-item" *ngFor="let skill of toolsSkills">
            <div class="skill-info">
              <div class="skill-name">{{ skill.name }}</div>
              <div class="skill-percentage">{{ skill.level }}%</div>
            </div>
            <div class="skill-bar">
              <div class="skill-progress" [style.width.%]="skill.level" [attr.data-level]="skill.level"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Technology Icons -->
    <div class="tech-showcase animate-on-scroll">
      <h3 class="showcase-title">Technologies I Work With</h3>
      <div class="tech-icons-grid">
        <div class="tech-icon-item" *ngFor="let tech of technologies">
          <div class="tech-icon-wrapper">
            <i [class]="tech.icon" [style.color]="tech.color"></i>
          </div>
          <span class="tech-name">{{ tech.name }}</span>
        </div>
      </div>
    </div>
  </div>
</div>

/* Enhanced Blue Skills Container */
.skills-container {
  padding: 5rem 0;
  background: var(--gradient-primary);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.skills-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 75% 70%, rgba(29, 78, 216, 0.12) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(96, 165, 250, 0.08) 0%, transparent 70%);
  animation: skillsBackgroundFlow 15s ease-in-out infinite;
}

.skills-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="skill-dots" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1.2" fill="%233b82f6" opacity="0.15"/><circle cx="7" cy="22" r="0.8" fill="%2360a5fa" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23skill-dots)"/></svg>');
  opacity: 0.4;
  animation: floatSkillPattern 20s linear infinite;
}

@keyframes skillsBackgroundFlow {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.01);
  }
}

@keyframes floatSkillPattern {
  0% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(-20px) translateY(-15px); }
  50% { transform: translateX(15px) translateY(-20px); }
  75% { transform: translateX(-15px) translateY(15px); }
  100% { transform: translateX(0) translateY(0); }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 3.5rem;
  font-weight: 700;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
  animation: skillsTitlePulse 4s ease-in-out infinite;
  position: relative;
}

@keyframes skillsTitlePulse {
  0%, 100% {
    filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.3));
    transform: scale(1);
  }
  50% {
    filter: drop-shadow(0 0 25px rgba(96, 165, 250, 0.5));
    transform: scale(1.02);
  }
}

.section-subtitle {
  font-size: 1.3rem;
  color: var(--text-blue-200);
  position: relative;
  opacity: 0.9;
}

.section-subtitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--gradient-accent);
  border-radius: 2px;
  box-shadow: 0 0 15px rgba(96, 165, 250, 0.4);
  animation: underlinePulse 3s ease-in-out infinite;
}

@keyframes underlinePulse {
  0%, 100% {
    width: 80px;
    box-shadow: 0 0 15px rgba(96, 165, 250, 0.4);
  }
  50% {
    width: 100px;
    box-shadow: 0 0 25px rgba(96, 165, 250, 0.6);
  }
}

/* Skills Content */
.skills-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 4rem;
}

/* Enhanced Skills Category */
.skills-category {
  background: rgba(30, 58, 138, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  border: 1px solid rgba(96, 165, 250, 0.2);
  box-shadow:
    0 20px 60px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transform: translateY(20px);
  opacity: 0;
  animation: slideUpFadeSkill 0.8s ease-out forwards;
}

.skills-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.5);
}

.skills-category::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(96, 165, 250, 0.03), transparent);
  animation: rotateSkillGlow 12s linear infinite;
  pointer-events: none;
}

.skills-category:nth-child(1) { animation-delay: 0.2s; }
.skills-category:nth-child(2) { animation-delay: 0.4s; }
.skills-category:nth-child(3) { animation-delay: 0.6s; }
.skills-category:nth-child(4) { animation-delay: 0.8s; }

@keyframes slideUpFadeSkill {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes rotateSkillGlow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.category-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.6rem;
  color: var(--text-white);
  margin-bottom: 2.5rem;
  font-weight: 600;
  position: relative;
}

.category-title i {
  font-size: 1.3rem;
  color: var(--blue-300);
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 10px rgba(96, 165, 250, 0.3));
}

/* Skills Grid */
.skills-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.skill-item {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.75rem;
  border: 1px solid rgba(96, 165, 250, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  transform: translateX(-20px);
  opacity: 0;
  animation: slideInSkill 0.6s ease-out forwards;
}

.skill-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.1), transparent);
  transition: left 0.6s ease;
}

.skill-item:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-3px) translateX(5px);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow:
    0 15px 50px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.skill-item:hover::before {
  left: 100%;
}

.skill-item:nth-child(1) { animation-delay: 0.1s; }
.skill-item:nth-child(2) { animation-delay: 0.2s; }
.skill-item:nth-child(3) { animation-delay: 0.3s; }
.skill-item:nth-child(4) { animation-delay: 0.4s; }
.skill-item:nth-child(5) { animation-delay: 0.5s; }
.skill-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInSkill {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.skill-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.skill-name {
  font-size: 1.1rem;
  color: var(--text-white);
  font-weight: 500;
  transition: color 0.3s ease;
}

.skill-percentage {
  font-size: 1rem;
  color: var(--blue-300);
  font-weight: 600;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 8px rgba(96, 165, 250, 0.3));
  transition: all 0.3s ease;
}

.skill-item:hover .skill-name {
  color: var(--blue-100);
}

.skill-item:hover .skill-percentage {
  filter: drop-shadow(0 0 12px rgba(96, 165, 250, 0.5));
}

/* Enhanced Skill Bar */
.skill-bar {
  height: 10px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.skill-progress {
  height: 100%;
  background: var(--gradient-accent);
  border-radius: 6px;
  position: relative;
  animation: fillBar 2.5s ease-out;
  transition: all 0.4s ease;
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.skill-progress::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.2) 100%);
  border-radius: 6px;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%);
  animation: shimmer 3s infinite;
  border-radius: 6px;
}

.skill-item:hover .skill-progress {
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: scaleY(1.1);
}

@keyframes fillBar {
  from {
    width: 0 !important;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Tech Showcase */
.tech-showcase {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.showcase-title {
  font-size: 2rem;
  color: white;
  margin-bottom: 3rem;
  font-weight: 600;
}

/* Tech Icons Grid */
.tech-icons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 2rem;
  justify-items: center;
}

.tech-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tech-icon-item:hover {
  transform: translateY(-10px);
}

.tech-icon-wrapper {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.tech-icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.tech-icon-item:hover .tech-icon-wrapper::before {
  transform: translateX(100%);
}

.tech-icon-item:hover .tech-icon-wrapper {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.tech-name {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  transition: color 0.3s ease;
}

.tech-icon-item:hover .tech-name {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .skills-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .skills-category {
    padding: 2rem;
  }

  .tech-showcase {
    padding: 2rem;
  }

  .tech-icons-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1.5rem;
  }

  .tech-icon-wrapper {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .container {
    padding: 0 1rem;
  }

  .skill-item {
    padding: 1rem;
  }
}

/* Animation Classes */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate {
  opacity: 1;
  transform: translateY(0);
}

.animate-on-scroll:nth-child(1) { transition-delay: 0.1s; }
.animate-on-scroll:nth-child(2) { transition-delay: 0.2s; }
.animate-on-scroll:nth-child(3) { transition-delay: 0.3s; }
.animate-on-scroll:nth-child(4) { transition-delay: 0.4s; }
.animate-on-scroll:nth-child(5) { transition-delay: 0.5s; }
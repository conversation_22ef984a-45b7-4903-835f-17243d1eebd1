/* Blue Skills Container */
.skills-container {
  padding: 5rem 0;
  background: linear-gradient(135deg, #0a0f2e 0%, #1e3a8a 100%);
  position: relative;
  overflow: hidden;
}

.skills-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(29, 78, 216, 0.15) 0%, transparent 50%),
              url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="blue-dots" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1.5" fill="%2360a5fa" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23blue-dots)"/></svg>');
  animation: skillsBackgroundFlow 12s ease-in-out infinite;
}

@keyframes skillsBackgroundFlow {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
  text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
  animation: blueSkillsTitleGlow 4s ease-in-out infinite;
}

@keyframes blueSkillsTitleGlow {
  0%, 100% {
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 40px rgba(255, 255, 255, 0.8), 0 0 60px rgba(96, 165, 250, 0.3);
  }
}

.section-subtitle {
  font-size: 1.3rem;
  color: #bfdbfe;
  position: relative;
}

.section-subtitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: rgba(191, 219, 254, 0.6);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(191, 219, 254, 0.4);
}

/* Skills Content */
.skills-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 4rem;
}

/* Skills Category */
.skills-category {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.category-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.5rem;
  color: white;
  margin-bottom: 2rem;
  font-weight: 600;
}

.category-title i {
  font-size: 1.2rem;
  color: #ffd700;
}

/* Skills Grid */
.skills-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.skill-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.skill-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.skill-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.skill-name {
  font-size: 1.1rem;
  color: white;
  font-weight: 500;
}

.skill-percentage {
  font-size: 1rem;
  color: #ffd700;
  font-weight: 600;
}

/* Skill Bar */
.skill-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, #ffd700 0%, #ffed4e 100%);
  border-radius: 4px;
  position: relative;
  animation: fillBar 2s ease-out;
  transition: width 0.3s ease;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes fillBar {
  from {
    width: 0 !important;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Tech Showcase */
.tech-showcase {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.showcase-title {
  font-size: 2rem;
  color: white;
  margin-bottom: 3rem;
  font-weight: 600;
}

/* Tech Icons Grid */
.tech-icons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 2rem;
  justify-items: center;
}

.tech-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tech-icon-item:hover {
  transform: translateY(-10px);
}

.tech-icon-wrapper {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.tech-icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.tech-icon-item:hover .tech-icon-wrapper::before {
  transform: translateX(100%);
}

.tech-icon-item:hover .tech-icon-wrapper {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.tech-name {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  transition: color 0.3s ease;
}

.tech-icon-item:hover .tech-name {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .skills-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .skills-category {
    padding: 2rem;
  }

  .tech-showcase {
    padding: 2rem;
  }

  .tech-icons-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1.5rem;
  }

  .tech-icon-wrapper {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .container {
    padding: 0 1rem;
  }

  .skill-item {
    padding: 1rem;
  }
}
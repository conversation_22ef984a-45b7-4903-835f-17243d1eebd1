import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Project {
  id: number;
  title: string;
  description: string;
  features: string[];
  technologies: string[];
  category: string;
  categoryLabel: string;
  githubUrl?: string;
  liveUrl?: string;
  icon: string;
  status: 'completed' | 'in-progress';
}

@Component({
  selector: 'app-projects',
  imports: [CommonModule],
  templateUrl: './projects.component.html',
  styleUrl: './projects.component.css'
})
export class ProjectsComponent implements OnInit {
  activeFilter: string = 'all';
  projects: Project[] = [
    {
      id: 1,
      title: 'WE CARE',
      description: '🏥 Revolutionary healthcare management system with AI-powered patient care optimization. Features real-time monitoring, smart scheduling, and predictive analytics for better health outcomes.',
      features: [
        '🤖 AI-powered patient diagnosis assistance',
        '📅 Smart appointment scheduling with conflict resolution',
        '📊 Real-time health monitoring dashboard',
        '💬 Secure doctor-patient communication portal',
        '💊 Intelligent prescription management system',
        '📈 Advanced analytics and reporting engine'
      ],
      technologies: ['Java', 'Spring Boot', 'Angular', 'MySQL', 'Bootstrap', 'REST API', 'JWT', 'WebSocket'],
      category: 'fullstack',
      categoryLabel: 'Full Stack',
      githubUrl: 'https://github.com/sachinprabashwara4/we-care',
      icon: 'fas fa-heartbeat',
      status: 'completed'
    },
    {
      id: 2,
      title: 'NITRO GASS',
      description: '🔥 Ultra-modern gas delivery ecosystem with IoT integration and real-time tracking. Revolutionary approach to gas distribution with smart inventory management.',
      features: [
        '🚚 Real-time GPS tracking with live updates',
        '📱 Mobile-first ordering experience',
        '🎯 Smart route optimization for delivery',
        '💳 Multiple payment gateway integration',
        '📊 Advanced inventory management system',
        '🔔 Push notifications and SMS alerts'
      ],
      technologies: ['Angular', 'TypeScript', 'Node.js', 'Express', 'MongoDB', 'Socket.io', 'PWA'],
      category: 'fullstack',
      categoryLabel: 'Full Stack',
      githubUrl: 'https://github.com/sachinprabashwara4/nitro-gas',
      icon: 'fas fa-fire',
      status: 'completed'
    },
    {
      id: 3,
      title: 'WEATHER WIZARD',
      description: '🌈 Next-gen weather application with stunning 3D visualizations and AI-powered predictions. Experience weather like never before with immersive graphics.',
      features: [
        '🌍 Interactive 3D weather globe',
        '🔮 AI-powered 15-day forecasting',
        '⚡ Real-time severe weather alerts',
        '📍 Hyper-local weather data (1km accuracy)',
        '🎨 Dynamic weather-based UI themes',
        '📊 Advanced weather analytics dashboard'
      ],
      technologies: ['JavaScript', 'HTML5', 'CSS3', 'Three.js', 'Weather API', 'Chart.js', 'WebGL'],
      category: 'frontend',
      categoryLabel: 'Frontend',
      githubUrl: 'https://github.com/sachinprabashwara4/weather-app',
      liveUrl: 'https://sachinprabashwara4.github.io/weather-app',
      icon: 'fas fa-cloud-sun',
      status: 'completed'
    },
    {
      id: 4,
      title: 'BURGER KINGDOM',
      description: '🍔 Immersive burger ordering experience with 3D menu visualization and gamified ordering process. The future of food ordering is here!',
      features: [
        '🎮 Gamified ordering experience with rewards',
        '🥩 3D burger customization interface',
        '💰 Dynamic pricing with real-time updates',
        '🚀 Lightning-fast checkout process',
        '📱 Progressive Web App capabilities',
        '🎯 Loyalty program with achievement system'
      ],
      technologies: ['HTML5', 'CSS3', 'JavaScript', 'Three.js', 'Bootstrap', 'jQuery', 'Local Storage', 'PWA'],
      category: 'frontend',
      categoryLabel: 'Frontend',
      githubUrl: 'https://github.com/sachinprabashwara4/burger-shop',
      liveUrl: 'https://sachinprabashwara4.github.io/burger-shop',
      icon: 'fas fa-hamburger',
      status: 'completed'
    },
    {
      id: 5,
      title: 'CRYPTO TRACKER PRO',
      description: '💎 Advanced cryptocurrency tracking platform with real-time market analysis, portfolio management, and AI-powered trading insights.',
      features: [
        '📈 Real-time crypto price tracking',
        '🤖 AI-powered market predictions',
        '💼 Advanced portfolio management',
        '🔔 Smart price alerts and notifications',
        '📊 Technical analysis tools',
        '🌐 Multi-exchange integration'
      ],
      technologies: ['React', 'TypeScript', 'Node.js', 'Express', 'MongoDB', 'WebSocket', 'Chart.js'],
      category: 'fullstack',
      categoryLabel: 'Full Stack',
      githubUrl: 'https://github.com/sachinprabashwara4/crypto-tracker',
      icon: 'fab fa-bitcoin',
      status: 'completed'
    },
    {
      id: 6,
      title: 'SMART LEARNING HUB',
      description: '🎓 Revolutionary e-learning platform with AI tutors, interactive content, and personalized learning paths for modern education.',
      features: [
        '🤖 AI-powered personal tutoring system',
        '🎯 Adaptive learning algorithms',
        '🎮 Gamified learning experience',
        '📹 Interactive video lessons',
        '📊 Advanced progress analytics',
        '👥 Collaborative study groups'
      ],
      technologies: ['Angular', 'TypeScript', 'Spring Boot', 'PostgreSQL', 'WebRTC', 'TensorFlow.js'],
      category: 'fullstack',
      categoryLabel: 'Full Stack',
      githubUrl: 'https://github.com/sachinprabashwara4/smart-learning',
      icon: 'fas fa-graduation-cap',
      status: 'completed'
    },
    {
      id: 7,
      title: 'SOCIAL MEDIA DASHBOARD',
      description: '📱 Unified social media management platform with advanced analytics, content scheduling, and engagement optimization tools.',
      features: [
        '📊 Multi-platform analytics dashboard',
        '⏰ Smart content scheduling',
        '🎯 Audience engagement insights',
        '📈 Growth tracking and reporting',
        '🤖 AI-powered content suggestions',
        '🔗 Cross-platform posting'
      ],
      technologies: ['Vue.js', 'TypeScript', 'Node.js', 'Express', 'Redis', 'Social APIs'],
      category: 'fullstack',
      categoryLabel: 'Full Stack',
      githubUrl: 'https://github.com/sachinprabashwara4/social-dashboard',
      icon: 'fas fa-share-alt',
      status: 'completed'
    },
    {
      id: 8,
      title: 'TASK MASTER 3D',
      description: '✅ Next-generation task management with 3D workspace visualization, AI prioritization, and immersive productivity tools.',
      features: [
        '🌐 3D workspace visualization',
        '🤖 AI-powered task prioritization',
        '⚡ Real-time collaboration tools',
        '📊 Advanced productivity analytics',
        '🎯 Goal tracking and achievements',
        '🔔 Smart notification system'
      ],
      technologies: ['React', 'Three.js', 'TypeScript', 'Node.js', 'MongoDB', 'Socket.io'],
      category: 'fullstack',
      categoryLabel: 'Full Stack',
      githubUrl: 'https://github.com/sachinprabashwara4/task-master',
      icon: 'fas fa-tasks',
      status: 'completed'
    }
  ];

  filteredProjects: Project[] = [];

  ngOnInit() {
    this.filteredProjects = this.projects;
  }

  filterProjects(category: string) {
    this.activeFilter = category;
    if (category === 'all') {
      this.filteredProjects = this.projects;
    } else {
      this.filteredProjects = this.projects.filter(project => project.category === category);
    }
  }

  getTechCount(): number {
    const allTechs = new Set<string>();
    this.projects.forEach(project => {
      project.technologies.forEach(tech => allTechs.add(tech));
    });
    return allTechs.size;
  }

  getCompletedProjects(): number {
    return this.projects.filter(project => project.status === 'completed').length;
  }
}

/* Enhanced Blue About Container */
.about-container {
  padding: 5rem 0;
  background: var(--gradient-primary);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.about-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 80% 70%, rgba(29, 78, 216, 0.12) 0%, transparent 60%),
    radial-gradient(circle at 40% 80%, rgba(96, 165, 250, 0.08) 0%, transparent 50%);
  animation: aboutBackgroundPulse 12s ease-in-out infinite;
}

.about-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="blue-dots" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="%233b82f6" opacity="0.1"/><circle cx="10" cy="30" r="0.5" fill="%2360a5fa" opacity="0.08"/></pattern></defs><rect width="100" height="100" fill="url(%23blue-dots)"/></svg>');
  opacity: 0.3;
  animation: floatPattern 20s linear infinite;
}

@keyframes aboutBackgroundPulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.02);
  }
}

@keyframes floatPattern {
  0% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(-10px) translateY(-5px); }
  50% { transform: translateX(5px) translateY(-10px); }
  75% { transform: translateX(-5px) translateY(5px); }
  100% { transform: translateX(0) translateY(0); }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 3.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #3b82f6 0%, #60a5fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
  animation: blueTitlePulse 4s ease-in-out infinite;
}

@keyframes blueTitlePulse {
  0%, 100% {
    filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.5));
  }
}

.section-subtitle {
  font-size: 1.3rem;
  color: #bfdbfe;
  position: relative;
}

.section-subtitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(45deg, #3b82f6 0%, #60a5fa 100%);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.4);
}

/* About Content */
.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Enhanced About Text */
.about-text {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 3rem;
  border-radius: 24px;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  transform: translateY(20px);
  opacity: 0;
  animation: slideUpFade 0.8s ease-out 0.3s forwards;
}

.about-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: var(--gradient-accent);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.about-text::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.03), transparent);
  animation: rotateGlow 10s linear infinite;
  pointer-events: none;
}

@keyframes slideUpFade {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes rotateGlow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.about-intro h3 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 1rem;
  font-weight: 600;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 2rem;
}

/* Enhanced Detail Grid */
.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.detail-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
  transition: left 0.6s ease;
}

.detail-item:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  transform: translateY(-3px);
  box-shadow:
    0 10px 40px rgba(59, 130, 246, 0.15),
    0 0 0 1px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.detail-item:hover::before {
  left: 100%;
}

.detail-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-accent);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  flex-shrink: 0;
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.detail-item:hover .detail-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow:
    0 12px 35px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.detail-content h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.2rem;
}

.detail-content p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* About Actions */
.about-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-secondary:hover {
  background: var(--gradient-accent);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Enhanced Visual Card */
.about-visual {
  transform: translateY(20px);
  opacity: 0;
  animation: slideUpFade 0.8s ease-out 0.6s forwards;
}

.visual-card {
  background: rgba(30, 58, 138, 0.95);
  backdrop-filter: blur(20px);
  padding: 3rem;
  border-radius: 24px;
  border: 1px solid rgba(96, 165, 250, 0.2);
  box-shadow:
    0 25px 80px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.visual-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: var(--gradient-accent);
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.5);
}

.visual-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 180deg, transparent, rgba(96, 165, 250, 0.05), transparent);
  animation: rotateGlow 15s linear infinite reverse;
  pointer-events: none;
}

.card-header {
  margin-bottom: 2.5rem;
  text-align: center;
}

.card-header h4 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-white);
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(96, 165, 250, 0.3);
  position: relative;
}

.card-header h4::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--gradient-accent);
  border-radius: 2px;
  box-shadow: 0 0 15px rgba(96, 165, 250, 0.4);
}

.services-list {
  display: grid;
  gap: 1.5rem;
}

.service-item {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(96, 165, 250, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.service-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.1), transparent);
  transition: left 0.6s ease;
}

.service-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(10px);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow:
    0 15px 50px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.service-item:hover::before {
  left: 100%;
}

.service-icon {
  width: 50px;
  height: 50px;
  background: var(--gradient-accent);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.3rem;
  flex-shrink: 0;
  box-shadow:
    0 10px 30px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.service-item:hover .service-icon {
  transform: scale(1.1) rotate(-5deg);
  box-shadow:
    0 15px 40px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.service-content h5 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.service-content p {
  font-size: 0.95rem;
  color: var(--text-blue-200);
  line-height: 1.6;
  margin: 0;
  transition: color 0.3s ease;
}

.service-item:hover .service-content h5 {
  color: var(--blue-100);
}

.service-item:hover .service-content p {
  color: var(--blue-100);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .about-container {
    padding: 4rem 0;
  }

  .about-content {
    gap: 3rem;
  }

  .section-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .about-container {
    padding: 3rem 0;
  }

  .container {
    padding: 0 1.5rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-subtitle {
    font-size: 1.1rem;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .about-text,
  .visual-card {
    padding: 2rem;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .about-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .btn {
    justify-content: center;
    width: 100%;
  }

  .service-item {
    padding: 1.25rem;
  }

  .service-icon {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .about-container {
    padding: 2rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .about-text,
  .visual-card {
    padding: 1.5rem;
  }

  .detail-item {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .detail-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .service-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .service-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Loading and Scroll Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Staggered Animation Classes */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate {
  opacity: 1;
  transform: translateY(0);
}

.animate-on-scroll:nth-child(1) { transition-delay: 0.1s; }
.animate-on-scroll:nth-child(2) { transition-delay: 0.2s; }
.animate-on-scroll:nth-child(3) { transition-delay: 0.3s; }
.animate-on-scroll:nth-child(4) { transition-delay: 0.4s; }
.animate-on-scroll:nth-child(5) { transition-delay: 0.5s; }
.animate-on-scroll:nth-child(6) { transition-delay: 0.6s; }
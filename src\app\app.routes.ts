import { Routes } from '@angular/router';
import { HeroComponent } from './components/hero/hero.component';
import { AboutComponent } from './components/about/about.component';
import { SkillsComponent } from './components/skills/skills.component';
import { EducationComponent } from './components/education/education.component';
import { ProjectsComponent } from './components/projects/projects.component';
import { ExperienceComponent } from './components/experience/experience.component';
import { ContactComponent } from './components/contact/contact.component';

export const routes: Routes = [
  // Home route - shows all sections in single page
  {
    path: '',
    redirectTo: '/home',
    pathMatch: 'full'
  },

  // Main portfolio page with all sections
  {
    path: 'home',
    component: HeroComponent,
    title: 'Sachin Prabashwara - Full Stack Developer'
  },

  // Individual section routes for direct navigation
  {
    path: 'about',
    component: AboutComponent,
    title: 'About - Sachin Prabashwara'
  },

  {
    path: 'skills',
    component: SkillsComponent,
    title: 'Skills & Technologies - Sachin Prabashwara'
  },

  {
    path: 'education',
    component: EducationComponent,
    title: 'Education - Sachin Prabashwara'
  },

  {
    path: 'projects',
    component: ProjectsComponent,
    title: 'Projects - Sachin Prabashwara'
  },

  {
    path: 'experience',
    component: ExperienceComponent,
    title: 'Experience - Sachin Prabashwara'
  },

  {
    path: 'contact',
    component: ContactComponent,
    title: 'Contact - Sachin Prabashwara'
  },

  // Wildcard route - must be last
  {
    path: '**',
    redirectTo: '/home',
    pathMatch: 'full'
  }
];

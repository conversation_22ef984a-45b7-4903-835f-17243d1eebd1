<div class="projects-container">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">My Projects</h2>
      <div class="section-subtitle">Showcasing my development skills through real-world applications</div>
    </div>

    <div class="projects-filter">
      <button
        class="filter-btn"
        [class.active]="activeFilter === 'all'"
        (click)="filterProjects('all')">
        All Projects
      </button>
      <button
        class="filter-btn"
        [class.active]="activeFilter === 'web'"
        (click)="filterProjects('web')">
        Web Apps
      </button>
      <button
        class="filter-btn"
        [class.active]="activeFilter === 'fullstack'"
        (click)="filterProjects('fullstack')">
        Full Stack
      </button>
      <button
        class="filter-btn"
        [class.active]="activeFilter === 'frontend'"
        (click)="filterProjects('frontend')">
        Frontend
      </button>
    </div>

    <div class="projects-grid">
      <div
        class="project-card"
        *ngFor="let project of filteredProjects"
        [attr.data-category]="project.category">

        <div class="project-image">
          <div class="project-placeholder">
            <i [class]="project.icon"></i>
            <span>{{ project.title }}</span>
          </div>
          <div class="project-overlay">
            <div class="project-actions">
              <a [href]="project.liveUrl" target="_blank" class="action-btn live" *ngIf="project.liveUrl">
                <i class="fas fa-external-link-alt"></i>
                <span>Live Demo</span>
              </a>
              <a [href]="project.githubUrl" target="_blank" class="action-btn github" *ngIf="project.githubUrl">
                <i class="fab fa-github"></i>
                <span>Source Code</span>
              </a>
            </div>
          </div>
        </div>

        <div class="project-content">
          <div class="project-header">
            <h3>{{ project.title }}</h3>
            <div class="project-category-badge">{{ project.categoryLabel }}</div>
          </div>

          <p class="project-description">{{ project.description }}</p>

          <div class="project-features">
            <h4>Key Features:</h4>
            <ul>
              <li *ngFor="let feature of project.features">{{ feature }}</li>
            </ul>
          </div>

          <div class="project-tech">
            <h4>Technologies Used:</h4>
            <div class="tech-stack">
              <span
                class="tech-tag"
                *ngFor="let tech of project.technologies">
                {{ tech }}
              </span>
            </div>
          </div>

          <div class="project-links">
            <a [href]="project.githubUrl" target="_blank" class="project-link github" *ngIf="project.githubUrl">
              <i class="fab fa-github"></i>
              <span>GitHub</span>
            </a>
            <a [href]="project.liveUrl" target="_blank" class="project-link live" *ngIf="project.liveUrl">
              <i class="fas fa-external-link-alt"></i>
              <span>Live Demo</span>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Project Stats -->
    <div class="project-stats">
      <div class="stat-item">
        <div class="stat-number">{{ projects.length }}</div>
        <div class="stat-label">Total Projects</div>
      </div>

      <div class="stat-item">
        <div class="stat-number">{{ getTechCount() }}</div>
        <div class="stat-label">Technologies Used</div>
      </div>

      <div class="stat-item">
        <div class="stat-number">{{ getCompletedProjects() }}</div>
        <div class="stat-label">Completed Projects</div>
      </div>

      <div class="stat-item">
        <div class="stat-number">100%</div>
        <div class="stat-label">Success Rate</div>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="cta-section">
      <h3>Interested in Working Together?</h3>
      <p>I'm always excited to take on new challenges and create amazing digital experiences.</p>
      <div class="cta-buttons">
        <a href="#contact" class="btn btn-primary">
          <i class="fas fa-envelope"></i>
          <span>Get In Touch</span>
        </a>
        <a href="https://github.com/sachinprabashwara4" target="_blank" class="btn btn-secondary">
          <i class="fab fa-github"></i>
          <span>View All Projects</span>
        </a>
      </div>
    </div>
  </div>
</div>
